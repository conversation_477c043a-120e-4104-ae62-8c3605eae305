<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - ReferralPro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .sidebar {
            background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
            border-right: 1px solid rgba(192, 192, 192, 0.1);
            padding: 30px 0;
        }
        
        .logo {
            padding: 0 30px 40px;
            font-size: 24px;
            font-weight: 800;
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin-bottom: 8px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 16px 30px;
            color: #a0a0a0;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: #ffffff;
            background: rgba(192, 192, 192, 0.1);
            border-left-color: #c0c0c0;
        }
        
        .nav-icon {
            margin-right: 12px;
            font-size: 18px;
        }
        
        /* Main Content */
        .main-content {
            padding: 30px;
            overflow-y: auto;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #c0c0c0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .admin-badge {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.05), rgba(255, 255, 255, 0.02));
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 16px;
            padding: 28px;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(192, 192, 192, 0.1);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .stat-label {
            color: #a0a0a0;
            font-size: 14px;
            font-weight: 500;
        }
        
        .stat-trend {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
        }
        
        .trend-up {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }
        
        .trend-down {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: #ffffff;
            margin-bottom: 8px;
        }
        
        .stat-description {
            color: #a0a0a0;
            font-size: 13px;
        }
        
        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .card {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.05), rgba(255, 255, 255, 0.02));
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 16px;
            padding: 28px;
        }
        
        .card-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ffffff;
        }
        
        /* Pending Approvals */
        .approval-item {
            background: rgba(192, 192, 192, 0.05);
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }
        
        .approval-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .approval-title {
            font-weight: 700;
            font-size: 16px;
            color: #ffffff;
            margin-bottom: 4px;
        }
        
        .approval-business {
            font-size: 14px;
            color: #a0a0a0;
        }
        
        .approval-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
        }
        
        .approval-details {
            margin-bottom: 16px;
            color: #a0a0a0;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .approval-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn-approve {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-reject {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-approve:hover,
        .btn-reject:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        
        /* Recent Activity */
        .activity-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(192, 192, 192, 0.1);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: #000;
            font-size: 16px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #ffffff;
        }
        
        .activity-time {
            font-size: 12px;
            color: #a0a0a0;
        }
        
        /* Platform Metrics */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .metric-item {
            background: rgba(192, 192, 192, 0.05);
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.8rem;
            font-weight: 800;
            color: #c0c0c0;
            margin-bottom: 8px;
        }
        
        .metric-label {
            color: #a0a0a0;
            font-size: 14px;
            font-weight: 500;
        }
        
        /* Full Width Section */
        .full-width-card {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.05), rgba(255, 255, 255, 0.02));
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 16px;
            padding: 28px;
            grid-column: 1 / -1;
        }
        
        .disputes-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .disputes-table th,
        .disputes-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid rgba(192, 192, 192, 0.1);
        }
        
        .disputes-table th {
            color: #a0a0a0;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .disputes-table td {
            color: #ffffff;
        }
        
        .dispute-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-open {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .status-investigating {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
        }
        
        .status-resolved {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }
        
        @media (max-width: 1024px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                display: none;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <aside class="sidebar">
            <div class="logo">ReferralPro</div>
            
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">✅</span>
                            Approvals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">👥</span>
                            Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Businesses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">💰</span>
                            Payouts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">⚖️</span>
                            Disputes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Analytics
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <main class="main-content">
            <div class="header">
                <h1 class="page-title">Admin Dashboard</h1>
                <div class="admin-badge">Administrator</div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-label">Platform Revenue</span>
                        <span class="stat-trend trend-up">+24.3%</span>
                    </div>
                    <div class="stat-value">$89,450</div>
                    <div class="stat-description">This month: $18,920</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-label">Active Users</span>
                        <span class="stat-trend trend-up">+156</span>
                    </div>
                    <div class="stat-value">2,847</div>
                    <div class="stat-description">1,234 referrers • 1,613 businesses</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-label">Pending Approvals</span>
                        <span class="stat-trend trend-down">-3</span>
                    </div>
                    <div class="stat-value">12</div>
                    <div class="stat-description">Requires attention</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-label">Success Rate</span>
                        <span class="stat-trend trend-up">+1.2%</span>
                    </div>
                    <div class="stat-value">91.4%</div>
                    <div class="stat-description">Platform average</div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="card">
                    <h2 class="card-title">Pending Approvals</h2>
                    
                    <div class="approval-item">
                        <div class="approval-header">
                            <div>
                                <div class="approval-title">Premium Yoga Studio Membership</div>
                                <div class="approval-business">ZenFlow Yoga • $85 per conversion</div>
                            </div>
                            <span class="approval-status">Pending</span>
                        </div>
                        <div class="approval-details">
                            New business offering 30-day unlimited yoga membership with $85 commission per successful referral. Budget: $5,000.
                        </div>
                        <div class="approval-actions">
                            <a href="#" class="btn-approve">Approve</a>
                            <a href="#" class="btn-reject">Reject</a>
                        </div>
                    </div>
                    
                    <div class="approval-item">
                        <div class="approval-header">
                            <div>
                                <div class="approval-title">Tech Bootcamp Enrollment</div>
                                <div class="approval-business">CodeMaster Academy • $250 per conversion</div>
                            </div>
                            <span class="approval-status">Pending</span>
                        </div>
                        <div class="approval-details">
                            12-week coding bootcamp with job guarantee. High-value offer with comprehensive curriculum and career support.
                        </div>
                        <div class="approval-actions">
                            <a href="#" class="btn-approve">Approve</a>
                            <a href="#" class="btn-reject">Reject</a>
                        </div>
                    </div>
                    
                    <div class="approval-item">
                        <div class="approval-header">
                            <div>
                                <div class="approval-title">Luxury Car Detailing Service</div>
                                <div class="approval-business">Elite Auto Care • $45 per conversion</div>
                            </div>
                            <span class="approval-status">Pending</span>
                        </div>
                        <div class="approval-details">
                            Premium car detailing service targeting luxury vehicle owners. Established business with excellent reviews.
                        </div>
                        <div class="approval-actions">
                            <a href="#" class="btn-approve">Approve</a>
                            <a href="#" class="btn-reject">Reject</a>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h2 class="card-title">Recent Platform Activity</h2>
                    
                    <div class="activity-item">
                        <div class="activity-icon">✅</div>
                        <div class="activity-content">
                            <div class="activity-title">Offer approved: Fitness Membership</div>
                            <div class="activity-time">15 minutes ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">👤</div>
                        <div class="activity-content">
                            <div class="activity-title">New business registered: TechStart Inc.</div>
                            <div class="activity-time">1 hour ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">⚖️</div>
                        <div class="activity-content">
                            <div class="activity-title">Dispute resolved: Referral #R-2847</div>
                            <div class="activity-time">2 hours ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">💰</div>
                        <div class="activity-content">
                            <div class="activity-title">Payout processed: $12,450</div>
                            <div class="activity-time">3 hours ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">🚫</div>
                        <div class="activity-content">
                            <div class="activity-title">Offer rejected: Suspicious business</div>
                            <div class="activity-time">4 hours ago</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="card">
                    <h2 class="card-title">Platform Metrics</h2>
                    
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value">$2.4M</div>
                            <div class="metric-label">Total Volume</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">15,847</div>
                            <div class="metric-label">Conversions</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">4.2%</div>
                            <div class="metric-label">Platform Fee</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">99.8%</div>
                            <div class="metric-label">Uptime</div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h2 class="card-title">Quality Scores</h2>
                    
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value">4.8/5</div>
                            <div class="metric-label">Business Rating</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">4.9/5</div>
                            <div class="metric-label">Referrer Rating</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">0.3%</div>
                            <div class="metric-label">Fraud Rate</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">2.1%</div>
                            <div class="metric-label">Dispute Rate</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="full-width-card">
                <h2 class="card-title">Active Disputes</h2>
                
                <table class="disputes-table">
                    <thead>
                        <tr>
                            <th>Dispute ID</th>
                            <th>Type</th>
                            <th>Referrer</th>
                            <th>Business</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>#D-2847</td>
                            <td>Conversion Dispute</td>
                            <td>Sarah Johnson</td>
                            <td>FitLife Gym</td>
                            <td>$75.00</td>
                            <td><span class="dispute-status status-investigating">Investigating</span></td>
                            <td>2 hours ago</td>
                        </tr>
                        <tr>
                            <td>#D-2846</td>
                            <td>Payment Issue</td>
                            <td>Mike Chen</td>
                            <td>TechCourse Pro</td>
                            <td>$150.00</td>
                            <td><span class="dispute-status status-open">Open</span></td>
                            <td>1 day ago</td>
                        </tr>
                        <tr>
                            <td>#D-2845</td>
                            <td>Quality Concern</td>
                            <td>Emma Davis</td>
                            <td>Restaurant Week</td>
                            <td>$25.00</td>
                            <td><span class="dispute-status status-resolved">Resolved</span></td>
                            <td>3 days ago</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </main>
    </div>
</body>
</html>
