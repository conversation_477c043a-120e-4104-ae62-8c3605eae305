<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ReferralPro - AI-Enhanced Referral Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
            color: #ffffff;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(192, 192, 192, 0.1);
            z-index: 1000;
            padding: 20px 0;
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 800;
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-links {
            display: flex;
            gap: 40px;
            list-style: none;
        }
        
        .nav-links a {
            color: #c0c0c0;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-links a:hover {
            color: #ffffff;
            transform: translateY(-2px);
        }
        
        .cta-button {
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            color: #000;
            padding: 12px 24px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(192, 192, 192, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(192, 192, 192, 0.4);
        }
        
        /* Hero Section */
        .hero {
            padding: 140px 0 80px;
            text-align: center;
            position: relative;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 800px;
            height: 800px;
            background: radial-gradient(circle, rgba(192, 192, 192, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
        }
        
        .hero h1 {
            font-size: 4.5rem;
            font-weight: 900;
            margin-bottom: 24px;
            background: linear-gradient(135deg, #ffffff, #c0c0c0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.1;
        }
        
        .hero p {
            font-size: 1.3rem;
            color: #a0a0a0;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }
        
        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 60px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            color: #000;
            padding: 16px 32px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 6px 25px rgba(192, 192, 192, 0.3);
        }
        
        .btn-secondary {
            background: transparent;
            color: #c0c0c0;
            padding: 16px 32px;
            border: 2px solid #c0c0c0;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 35px rgba(192, 192, 192, 0.4);
        }
        
        .btn-secondary:hover {
            background: #c0c0c0;
            color: #000;
            transform: translateY(-3px);
        }
        
        /* Dashboard Preview */
        .dashboard-preview {
            position: relative;
            margin-top: 80px;
            perspective: 1000px;
        }
        
        .dashboard-mockup {
            background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(192, 192, 192, 0.1);
            transform: rotateX(5deg) rotateY(-5deg);
            transition: all 0.3s ease;
        }
        
        .dashboard-mockup:hover {
            transform: rotateX(0deg) rotateY(0deg);
        }
        
        .dashboard-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(192, 192, 192, 0.1);
        }
        
        .dashboard-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #ffffff;
        }
        
        .ai-badge {
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            color: #000;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(192, 192, 192, 0.05);
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2.2rem;
            font-weight: 800;
            color: #c0c0c0;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #a0a0a0;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        /* Features Section */
        .features {
            padding: 100px 0;
            background: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }
        
        .feature-card {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.05), rgba(255, 255, 255, 0.02));
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(192, 192, 192, 0.1);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            border-radius: 20px;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 16px;
            color: #ffffff;
        }
        
        .feature-description {
            color: #a0a0a0;
            line-height: 1.6;
        }
        
        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff, #c0c0c0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #a0a0a0;
            max-width: 600px;
            margin: 0 auto;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 3rem;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-links {
                display: none;
            }
            
            .dashboard-mockup {
                transform: none;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">ReferralPro</div>
                <ul class="nav-links">
                    <li><a href="#features">Features</a></li>
                    <li><a href="#pricing">Pricing</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <a href="#" class="cta-button">Get Started</a>
            </nav>
        </div>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <h1>The Future of<br>Referral Marketing</h1>
                <p>AI-powered platform where anyone can earn by sharing real business offers. Businesses only pay for confirmed results. The Uber for referrals is here.</p>
                
                <div class="hero-buttons">
                    <a href="#" class="btn-primary">Start Earning Today</a>
                    <a href="#" class="btn-secondary">List Your Business</a>
                </div>
                
                <div class="dashboard-preview">
                    <div class="dashboard-mockup">
                        <div class="dashboard-header">
                            <div class="dashboard-title">Referrer Dashboard</div>
                            <div class="ai-badge">🤖 AI Assistant</div>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">$2,847</div>
                                <div class="stat-label">Total Earnings</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">Successful Referrals</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">Gold</div>
                                <div class="stat-label">Current Rank</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">94%</div>
                                <div class="stat-label">Success Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="features" id="features">
            <div class="container">
                <h2 class="section-title">Powered by AI</h2>
                <p class="section-subtitle">Advanced artificial intelligence helps optimize every aspect of your referral strategy</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3 class="feature-title">Smart Deal Matching</h3>
                        <p class="feature-description">AI analyzes your network and suggests the most profitable deals to share based on your audience and past performance.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">💰</div>
                        <h3 class="feature-title">Instant Payouts</h3>
                        <p class="feature-description">Stripe Connect ensures you get paid immediately when referrals convert. No waiting, no hassle, just earnings.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3 class="feature-title">Performance Analytics</h3>
                        <p class="feature-description">Real-time tracking and AI-powered insights help you understand what works and optimize your strategy.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">🚀</div>
                        <h3 class="feature-title">Growth Automation</h3>
                        <p class="feature-description">AI generates social media captions, suggests posting times, and predicts earning potential for maximum growth.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">🛡️</div>
                        <h3 class="feature-title">Trust & Security</h3>
                        <p class="feature-description">Advanced fraud detection and quality scoring ensure fair play and protect both referrers and businesses.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">🎮</div>
                        <h3 class="feature-title">Gamified Experience</h3>
                        <p class="feature-description">Rank up from Bronze to Mega level, earn badges, and compete on leaderboards while building your referral empire.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>
</body>
</html>
