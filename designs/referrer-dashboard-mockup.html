<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referrer Dashboard - ReferralPro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }
        
        /* Sidebar */
        .sidebar {
            background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
            border-right: 1px solid rgba(192, 192, 192, 0.1);
            padding: 30px 0;
        }
        
        .logo {
            padding: 0 30px 40px;
            font-size: 24px;
            font-weight: 800;
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin-bottom: 8px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 16px 30px;
            color: #a0a0a0;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: #ffffff;
            background: rgba(192, 192, 192, 0.1);
            border-left-color: #c0c0c0;
        }
        
        .nav-icon {
            margin-right: 12px;
            font-size: 18px;
        }
        
        .user-profile {
            position: absolute;
            bottom: 30px;
            left: 30px;
            right: 30px;
            display: flex;
            align-items: center;
            padding: 16px;
            background: rgba(192, 192, 192, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(192, 192, 192, 0.1);
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
            font-weight: 700;
        }
        
        .user-info h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .user-rank {
            font-size: 12px;
            color: #c0c0c0;
            font-weight: 500;
        }
        
        /* Main Content */
        .main-content {
            padding: 30px;
            overflow-y: auto;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff, #c0c0c0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .ai-assistant {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(192, 192, 192, 0.2);
            border-radius: 50px;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .ai-assistant:hover {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.2), rgba(255, 255, 255, 0.1));
            transform: translateY(-2px);
        }
        
        .ai-icon {
            margin-right: 8px;
            font-size: 18px;
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.05), rgba(255, 255, 255, 0.02));
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 16px;
            padding: 28px;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(192, 192, 192, 0.1);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .stat-label {
            color: #a0a0a0;
            font-size: 14px;
            font-weight: 500;
        }
        
        .stat-trend {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 600;
        }
        
        .trend-up {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }
        
        .trend-down {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: #ffffff;
            margin-bottom: 8px;
        }
        
        .stat-description {
            color: #a0a0a0;
            font-size: 13px;
        }
        
        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .card {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.05), rgba(255, 255, 255, 0.02));
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 16px;
            padding: 28px;
        }
        
        .card-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ffffff;
        }
        
        /* Recent Activity */
        .activity-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(192, 192, 192, 0.1);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: #000;
            font-size: 16px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 600;
            margin-bottom: 4px;
            color: #ffffff;
        }
        
        .activity-time {
            font-size: 12px;
            color: #a0a0a0;
        }
        
        .activity-amount {
            font-weight: 700;
            color: #22c55e;
        }
        
        /* AI Suggestions */
        .suggestion-item {
            background: rgba(192, 192, 192, 0.05);
            border: 1px solid rgba(192, 192, 192, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
        }
        
        .suggestion-item:hover {
            background: rgba(192, 192, 192, 0.1);
            transform: translateX(4px);
        }
        
        .suggestion-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #ffffff;
        }
        
        .suggestion-description {
            font-size: 14px;
            color: #a0a0a0;
            margin-bottom: 12px;
        }
        
        .suggestion-potential {
            font-size: 12px;
            color: #22c55e;
            font-weight: 600;
        }
        
        .btn-small {
            background: linear-gradient(135deg, #c0c0c0, #ffffff);
            color: #000;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
            margin-top: 8px;
        }
        
        .btn-small:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(192, 192, 192, 0.3);
        }
        
        @media (max-width: 1024px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                display: none;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <aside class="sidebar">
            <div class="logo">ReferralPro</div>
            
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">🎯</span>
                            Available Deals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">📈</span>
                            My Referrals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">💰</span>
                            Earnings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">🏆</span>
                            Leaderboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">🤖</span>
                            AI Assistant
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="user-profile">
                <div class="avatar">JS</div>
                <div class="user-info">
                    <h4>John Smith</h4>
                    <div class="user-rank">Gold Referrer</div>
                </div>
            </div>
        </aside>
        
        <main class="main-content">
            <div class="header">
                <h1 class="page-title">Dashboard</h1>
                <div class="ai-assistant">
                    <span class="ai-icon">🤖</span>
                    <span>AI Assistant</span>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-label">Total Earnings</span>
                        <span class="stat-trend trend-up">+12.5%</span>
                    </div>
                    <div class="stat-value">$2,847.50</div>
                    <div class="stat-description">This month: $485.20</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-label">Active Referrals</span>
                        <span class="stat-trend trend-up">+8</span>
                    </div>
                    <div class="stat-value">23</div>
                    <div class="stat-description">Pending conversions</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-label">Success Rate</span>
                        <span class="stat-trend trend-up">+2.1%</span>
                    </div>
                    <div class="stat-value">94.2%</div>
                    <div class="stat-description">Above average</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-label">Rank Progress</span>
                        <span class="stat-trend trend-up">85%</span>
                    </div>
                    <div class="stat-value">Gold</div>
                    <div class="stat-description">15% to Platinum</div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="card">
                    <h2 class="card-title">Recent Activity</h2>
                    
                    <div class="activity-item">
                        <div class="activity-icon">💰</div>
                        <div class="activity-content">
                            <div class="activity-title">Referral Converted - <span class="activity-amount">+$45.00</span></div>
                            <div class="activity-time">2 hours ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">🎯</div>
                        <div class="activity-content">
                            <div class="activity-title">New referral link shared</div>
                            <div class="activity-time">5 hours ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">🏆</div>
                        <div class="activity-content">
                            <div class="activity-title">Rank upgraded to Gold</div>
                            <div class="activity-time">1 day ago</div>
                        </div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon">💰</div>
                        <div class="activity-content">
                            <div class="activity-title">Referral Converted - <span class="activity-amount">+$125.00</span></div>
                            <div class="activity-time">2 days ago</div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h2 class="card-title">AI Suggestions</h2>
                    
                    <div class="suggestion-item">
                        <div class="suggestion-title">Premium Fitness App</div>
                        <div class="suggestion-description">High conversion rate in your network. Perfect for health-conscious audience.</div>
                        <div class="suggestion-potential">Potential: $75-150/conversion</div>
                        <a href="#" class="btn-small">Share Now</a>
                    </div>
                    
                    <div class="suggestion-item">
                        <div class="suggestion-title">Local Restaurant Deal</div>
                        <div class="suggestion-description">Trending in your area. Great for weekend social posts.</div>
                        <div class="suggestion-potential">Potential: $25-40/conversion</div>
                        <a href="#" class="btn-small">Share Now</a>
                    </div>
                    
                    <div class="suggestion-item">
                        <div class="suggestion-title">Tech Course Bundle</div>
                        <div class="suggestion-description">Matches your professional network interests.</div>
                        <div class="suggestion-potential">Potential: $200-350/conversion</div>
                        <a href="#" class="btn-small">Share Now</a>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
